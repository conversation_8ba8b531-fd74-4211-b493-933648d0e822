/* Modern, Accessible Flight Search App Styles */

/* Typography */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #374151;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.25;
  color: #111827;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* Skip to main content link for screen readers */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #3B82F6;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* Form validation styles */
.error {
  border-color: #EF4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-message {
  color: #EF4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  body {
    background: white;
    color: black;
  }

  .bg-gray-50 {
    background: #f8f9fa !important;
  }

  .border-gray-300 {
    border-color: #000 !important;
  }
}

/* Beautiful blob animations for background */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

.animation-delay-600 {
  animation-delay: 600ms;
}

/* Enhanced bounce animation */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.animate-bounce {
  animation: bounce 2s infinite;
}

/* Shimmer effect for buttons */
@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}

/* Enhanced hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Floating animation for logo */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Gradient text animation */
@keyframes gradient {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

.animate-gradient {
  animation: gradient 3s ease infinite;
}

/* Enhanced custom scrollbar */
.autocomplete-items::-webkit-scrollbar {
  width: 8px;
}

.autocomplete-items::-webkit-scrollbar-track {
  background: linear-gradient(to bottom, #f8fafc, #e2e8f0);
  border-radius: 10px;
}

.autocomplete-items::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #cbd5e1, #94a3b8);
  border-radius: 10px;
  border: 2px solid #f8fafc;
}

.autocomplete-items::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #94a3b8, #64748b);
}

/* Shimmer loading effect */
@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.shimmer {
  background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
  background-size: 800px 104px;
  animation: shimmer 1.5s ease-in-out infinite;
}

/* Pulse animation for loading states */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Enhanced focus styles with glow */
input:focus, select:focus, textarea:focus {
  outline: none;
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.1),
    0 0 20px rgba(59, 130, 246, 0.2),
    0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Button hover effects */
button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

button:active {
  transform: translateY(0);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Enhanced Autocomplete dropdown styles */
.autocomplete {
  position: relative;
}

.autocomplete-items {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(59, 130, 246, 0.1);
  border-radius: 1rem;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  max-height: 250px;
  overflow-y: auto;
  margin-top: 8px;
  opacity: 0;
  transform: translateY(-10px);
  animation: slideDown 0.3s ease-out forwards;
}

@keyframes slideDown {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.autocomplete-item {
  padding: 1rem 1.25rem;
  cursor: pointer;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  font-size: 0.875rem;
  line-height: 1.5rem;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.autocomplete-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.autocomplete-item:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  transform: translateX(4px);
  border-left: 3px solid #3b82f6;
  padding-left: 1rem;
}

.autocomplete-item:hover::before {
  left: 100%;
}

.autocomplete-item:last-child {
  border-bottom: none;
  border-radius: 0 0 1rem 1rem;
}

.autocomplete-item:first-child {
  border-radius: 1rem 1rem 0 0;
}

/* Enhanced table row hover effects */
tbody tr {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

tbody tr:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  transform: translateY(-2px) scale(1.01);
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(59, 130, 246, 0.1);
  border-radius: 0.5rem;
}

tbody tr:hover td:first-child {
  border-radius: 0.5rem 0 0 0.5rem;
}

tbody tr:hover td:last-child {
  border-radius: 0 0.5rem 0.5rem 0;
}

/* Loading spinner styles */
.loading-spinner.opacity-100 {
  opacity: 1 !important;
}

.loading-spinner.opacity-100 ~ div {
  opacity: 0;
}

/* Passenger increment/decrement buttons */
.passenger-increment, .passenger-decrement {
  transition: all 0.2s ease;
  padding: 2px;
  border-radius: 4px;
}

.passenger-increment:hover, .passenger-decrement:hover {
  background-color: rgba(249, 115, 22, 0.1);
  transform: scale(1.1);
}

/* Hide the old airport results boxes */
.airport-results-box {
  display: none !important;
}

/* Enhanced responsive design */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Global icon size reduction for mobile */
  svg {
    width: 0.875rem !important;
    height: 0.875rem !important;
  }

  /* Specific icon size overrides */
  .mobile-icon-sm,
  label svg,
  input + svg {
    width: 0.75rem !important;
    height: 0.75rem !important;
  }

  /* Mobile form adjustments */
  input[type="text"], input[type="date"], input[type="number"] {
    font-size: 1rem !important;
    padding: 0.75rem 1rem !important;
  }

  input[type="text"] {
    padding-left: 2rem !important;
  }

  /* Force grid layouts to stack on mobile */
  .grid-cols-1.lg\\:grid-cols-2,
  .grid-cols-1.lg\\:grid-cols-3,
  .grid-cols-1.md\\:grid-cols-2,
  .grid-cols-1.md\\:grid-cols-3 {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  /* Mobile autocomplete */
  .autocomplete-item {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  /* Mobile table adjustments */
  tbody tr:hover {
    transform: none;
    scale: 1;
  }

  /* Hide background animations on mobile */
  .animate-blob {
    display: none;
  }

  /* Mobile logo size control */
  #logo-container .w-16 {
    width: 2.5rem !important;
    height: 2.5rem !important;
  }

  #logo-container .w-7 {
    width: 1.25rem !important;
    height: 1.25rem !important;
  }

  #logo-container .w-4 {
    width: 0.75rem !important;
    height: 0.75rem !important;
  }

  /* Mobile title adjustments */
  h1 {
    font-size: 2.5rem !important;
    line-height: 1.2 !important;
  }

  /* Mobile form enhancements */
  .w-8.h-8 {
    width: 1.5rem !important;
    height: 1.5rem !important;
  }

  .w-6.h-6 {
    width: 1rem !important;
    height: 1rem !important;
  }

  /* Mobile button adjustments */
  button {
    padding: 1rem 2rem !important;
    font-size: 1rem !important;
  }

  button .w-8 {
    width: 1.5rem !important;
    height: 1.5rem !important;
  }

  button .w-5 {
    width: 1rem !important;
    height: 1rem !important;
  }

  /* Mobile features section */
  .features-icon {
    width: 1rem !important;
    height: 1rem !important;
  }

  /* Mobile spacing adjustments */
  .space-y-10 > * + * {
    margin-top: 2rem !important;
  }

  .gap-8 {
    gap: 1.5rem !important;
  }
}

/* Custom shadow utilities */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Glassmorphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Fade in up animation for flight results */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
}

/* Enhanced button styles */
.btn-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Notification animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-enter {
  animation: slideInRight 0.3s ease-out;
}

/* Enhanced input focus effects */
.group.focused input {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Floating elements */
.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* Staggered animation delays */
.delay-100 { animation-delay: 100ms; }
.delay-200 { animation-delay: 200ms; }
.delay-300 { animation-delay: 300ms; }
.delay-400 { animation-delay: 400ms; }
.delay-500 { animation-delay: 500ms; }

/* Advanced Layout Improvements */

/* Card hover effects with depth */
.card-hover {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Improved spacing and rhythm */
.section-spacing {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

@media (min-width: 768px) {
  .section-spacing {
    padding-top: 7rem;
    padding-bottom: 7rem;
  }
}

/* Enhanced grid layouts */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Improved form layouts */
.form-group {
  position: relative;
  margin-bottom: 1.5rem;
}

.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label {
  transform: translateY(-1.5rem) scale(0.875);
  color: #3b82f6;
}

/* Enhanced table styling */
.table-enhanced {
  border-spacing: 0 0.5rem;
  border-collapse: separate;
}

.table-enhanced tbody tr {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 0.5rem;
}

.table-enhanced tbody tr:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Improved button groups */
.btn-group {
  display: inline-flex;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-group button {
  border-radius: 0;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-group button:first-child {
  border-radius: 0.75rem 0 0 0.75rem;
}

.btn-group button:last-child {
  border-radius: 0 0.75rem 0.75rem 0;
  border-right: none;
}

/* Enhanced loading states */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Improved scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Enhanced focus indicators */
.focus-ring:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow:
    0 0 0 2px white,
    0 0 0 4px #3b82f6,
    0 0 0 6px rgba(59, 130, 246, 0.3);
}

/* Advanced responsive utilities */
@media (max-width: 640px) {
  .mobile-stack > * {
    width: 100% !important;
    margin-bottom: 1rem;
  }

  .mobile-center {
    text-align: center;
  }

  .mobile-hide {
    display: none;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .autocomplete-item:hover {
    background-color: #000;
    color: #fff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .animate-blob,
  .animate-pulse,
  .animate-spin {
    animation: none !important;
  }
}
  