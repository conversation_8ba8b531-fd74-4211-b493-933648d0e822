/* Custom styles to complement Tailwind CSS */

/* Smooth transitions for all interactive elements */
* {
  transition: all 0.2s ease-in-out;
}

/* Custom scrollbar for autocomplete */
.autocomplete-items::-webkit-scrollbar {
  width: 6px;
}

.autocomplete-items::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.autocomplete-items::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.autocomplete-items::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom focus styles */
input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Hover effects for table rows */
tbody tr:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Autocomplete dropdown styles */
.autocomplete {
  position: relative;
}

.autocomplete-items {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  max-height: 200px;
  overflow-y: auto;
  margin-top: 4px;
}

.autocomplete-item {
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.autocomplete-item:hover {
  background-color: #f3f4f6;
}

.autocomplete-item:last-child {
  border-bottom: none;
}

/* Hide the old airport results boxes */
.airport-results-box {
  display: none !important;
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .grid-cols-1.md\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }

  .grid-cols-1.md\\:grid-cols-3 {
    grid-template-columns: 1fr;
  }
}
  