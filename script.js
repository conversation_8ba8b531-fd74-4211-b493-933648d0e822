// Flight Search Application with Local Backend API
const API_BASE_URL = 'http://localhost:3000/api';

let airportCache = {};

async function searchAirports(query) {
  if (airportCache[query]) {
    return airportCache[query];
  }

  try {
    const response = await fetch(`${API_BASE_URL}/airports/search?q=${encodeURIComponent(query)}`);
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
    }
    const data = await response.json();
    airportCache[query] = data;
    return data;
  } catch (error) {
    console.error("Error searching airports:", error);
    return [];
  }
}

async function fetchAirportData(iataCode) {
  if (airportCache[iataCode]) {
    return airportCache[iataCode];
  }

  try {
    const response = await fetch(`${API_BASE_URL}/airports/${iataCode}`);
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
    }
    const data = await response.json();
    airportCache[iataCode] = data;
    return data;
  } catch (error) {
    console.error("Error fetching airport data:", error);
    return null;
  }
}
  
function autocomplete(input, resultsBox) {
  input.addEventListener("input", debounce(async function () {
    const query = input.value.trim();
    resultsBox.innerHTML = '';

    if (query.length < 2) {
      resultsBox.style.display = 'none';
      return;
    }

    const airports = await searchAirports(query);

    if (airports && airports.length > 0) {
      airports.forEach(airport => {
        const option = document.createElement('div');
        option.innerHTML = `<strong>${airport.name}</strong> (${airport.iata}) - ${airport.city}, ${airport.country}`;
        option.classList.add('autocomplete-item');
        option.addEventListener('click', function () {
          input.value = `${airport.name} (${airport.iata})`;
          input.dataset.iata = airport.iata;
          resultsBox.innerHTML = '';
          resultsBox.style.display = 'none';
        });
        resultsBox.appendChild(option);
      });

      resultsBox.style.display = 'block';
    } else {
      resultsBox.innerHTML = '<div class="autocomplete-item">No airports found</div>';
      resultsBox.style.display = 'block';
    }
  }, 300));

  // Hide dropdown when clicking outside
  document.addEventListener('click', function(e) {
    if (!input.contains(e.target) && !resultsBox.contains(e.target)) {
      resultsBox.style.display = 'none';
    }
  });
}
  
  // Credits : https://www.freecodecamp.org/news/javascript-debounce-example/
  function debounce(func, delay) {
    let timeout;
    return function(...args) {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), delay);
    };
  }
  
  const departureInput = document.getElementById('departureInput');
  const departureResultsBox = document.getElementById('departureAirportResults');
  autocomplete(departureInput, departureResultsBox);
  
  const destinationInput = document.getElementById('destinationInput');
  const destinationResultsBox = document.getElementById('destinationAirportResults');
  autocomplete(destinationInput, destinationResultsBox); // Call is needed
  
// Flight search functionality
async function searchFlights(searchParams) {
  try {
    const queryParams = new URLSearchParams(searchParams);
    const response = await fetch(`${API_BASE_URL}/flights/search?${queryParams}`);

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return data.flights || [];
  } catch (error) {
    console.error("Error searching flights:", error);
    return [];
  }
}

// Flight Results
document.addEventListener('DOMContentLoaded', () => {
  const flightSearchForm = document.getElementById('flight-search-form');
  const flightResultsSection = document.getElementById('flight-results');
  const flightResultsBody = flightResultsSection.querySelector('tbody');

  flightSearchForm.addEventListener('submit', async (event) => {
    event.preventDefault();

    // Get form data
    const departureInput = document.getElementById('departureInput');
    const destinationInput = document.getElementById('destinationInput');
    const departureDate = document.getElementById('departure-date').value;
    const returnDate = document.getElementById('return-date').value;
    const passengers = document.getElementById('passengers').value;

    // Extract IATA codes from inputs
    const fromIata = departureInput.dataset.iata;
    const toIata = destinationInput.dataset.iata;

    if (!fromIata || !toIata) {
      alert('Please select valid departure and destination airports from the dropdown.');
      return;
    }

    // Show loading state
    flightResultsBody.innerHTML = '<tr><td colspan="4" class="text-center py-8">Searching flights...</td></tr>';

    // Search flights
    const searchParams = {
      from: fromIata,
      to: toIata,
      departure_date: departureDate,
      return_date: returnDate,
      passengers: passengers
    };

    const flights = await searchFlights(searchParams);
    displayFlightResults(flights);
  });

  function displayFlightResults(flights) {
    flightResultsBody.innerHTML = ""; // Clear previous results

    if (flights.length === 0) {
      const noResultsRow = flightResultsBody.insertRow();
      const noResultsCell = noResultsRow.insertCell();
      noResultsCell.colSpan = 4;
      noResultsCell.className = "text-center py-8 text-gray-500";
      noResultsCell.textContent = "No flights found for your search criteria.";
      return;
    }

    flights.forEach(flight => {
      const row = flightResultsBody.insertRow();
      row.className = "hover:bg-gray-50 transition-colors duration-200";

      const airlineCell = row.insertCell();
      airlineCell.className = "px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900";
      airlineCell.textContent = flight.airline;

      const priceCell = row.insertCell();
      priceCell.className = "px-6 py-4 whitespace-nowrap text-sm text-green-600 font-semibold";
      priceCell.textContent = flight.price;

      const departureCell = row.insertCell();
      departureCell.className = "px-6 py-4 whitespace-nowrap text-sm text-gray-500";
      departureCell.textContent = flight.departure;

      const arrivalCell = row.insertCell();
      arrivalCell.className = "px-6 py-4 whitespace-nowrap text-sm text-gray-500";
      arrivalCell.textContent = flight.arrival;
    });
  }
});
