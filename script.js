// Flight Search Application with Local Backend API
const API_BASE_URL = 'http://localhost:3000/api';

let airportCache = {};

async function searchAirports(query) {
  if (airportCache[query]) {
    return airportCache[query];
  }

  try {
    const response = await fetch(`${API_BASE_URL}/airports/search?q=${encodeURIComponent(query)}`);
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
    }
    const data = await response.json();
    airportCache[query] = data;
    return data;
  } catch (error) {
    console.error("Error searching airports:", error);
    return [];
  }
}

async function fetchAirportData(iataCode) {
  if (airportCache[iataCode]) {
    return airportCache[iataCode];
  }

  try {
    const response = await fetch(`${API_BASE_URL}/airports/${iataCode}`);
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
    }
    const data = await response.json();
    airportCache[iataCode] = data;
    return data;
  } catch (error) {
    console.error("Error fetching airport data:", error);
    return null;
  }
}
  
function autocomplete(input, resultsBox) {
  input.addEventListener("input", debounce(async function () {
    const query = input.value.trim();
    resultsBox.innerHTML = '';

    if (query.length < 2) {
      resultsBox.style.display = 'none';
      return;
    }

    const airports = await searchAirports(query);

    if (airports && airports.length > 0) {
      airports.forEach(airport => {
        const option = document.createElement('div');
        option.innerHTML = `<strong>${airport.name}</strong> (${airport.iata}) - ${airport.city}, ${airport.country}`;
        option.classList.add('autocomplete-item');
        option.addEventListener('click', function () {
          input.value = `${airport.name} (${airport.iata})`;
          input.dataset.iata = airport.iata;
          resultsBox.innerHTML = '';
          resultsBox.style.display = 'none';
        });
        resultsBox.appendChild(option);
      });

      resultsBox.style.display = 'block';
    } else {
      resultsBox.innerHTML = '<div class="autocomplete-item">No airports found</div>';
      resultsBox.style.display = 'block';
    }
  }, 300));

  // Hide dropdown when clicking outside
  document.addEventListener('click', function(e) {
    if (!input.contains(e.target) && !resultsBox.contains(e.target)) {
      resultsBox.style.display = 'none';
    }
  });
}
  
  // Credits : https://www.freecodecamp.org/news/javascript-debounce-example/
  function debounce(func, delay) {
    let timeout;
    return function(...args) {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), delay);
    };
  }
  
  const departureInput = document.getElementById('departureInput');
  const departureResultsBox = document.getElementById('departureAirportResults');
  autocomplete(departureInput, departureResultsBox);
  
  const destinationInput = document.getElementById('destinationInput');
  const destinationResultsBox = document.getElementById('destinationAirportResults');
  autocomplete(destinationInput, destinationResultsBox); // Call is needed
  
// Flight search functionality
async function searchFlights(searchParams) {
  try {
    const queryParams = new URLSearchParams(searchParams);
    const response = await fetch(`${API_BASE_URL}/flights/search?${queryParams}`);

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return data.flights || [];
  } catch (error) {
    console.error("Error searching flights:", error);
    return [];
  }
}

// Enhanced Interactive Features
document.addEventListener('DOMContentLoaded', () => {
  const flightSearchForm = document.getElementById('flight-search-form');
  const flightResultsSection = document.getElementById('flight-results');
  const flightResultsBody = flightResultsSection.querySelector('tbody');
  const passengersInput = document.getElementById('passengers');

  // Passenger increment/decrement functionality
  const incrementBtn = document.querySelector('.passenger-increment');
  const decrementBtn = document.querySelector('.passenger-decrement');

  if (incrementBtn) {
    incrementBtn.addEventListener('click', (e) => {
      e.preventDefault();
      const currentValue = parseInt(passengersInput.value);
      if (currentValue < 9) {
        passengersInput.value = currentValue + 1;
        passengersInput.dispatchEvent(new Event('change'));
      }
    });
  }

  if (decrementBtn) {
    decrementBtn.addEventListener('click', (e) => {
      e.preventDefault();
      const currentValue = parseInt(passengersInput.value);
      if (currentValue > 1) {
        passengersInput.value = currentValue - 1;
        passengersInput.dispatchEvent(new Event('change'));
      }
    });
  }

  // Add floating label effect
  const inputs = document.querySelectorAll('input[type="text"], input[type="date"], input[type="number"]');
  inputs.forEach(input => {
    input.addEventListener('focus', () => {
      input.parentElement.classList.add('focused');
    });

    input.addEventListener('blur', () => {
      if (!input.value) {
        input.parentElement.classList.remove('focused');
      }
    });
  });

  flightSearchForm.addEventListener('submit', async (event) => {
    event.preventDefault();

    // Get form elements
    const submitButton = event.target.querySelector('button[type="submit"]');
    const buttonContent = submitButton.querySelector('div');
    const loadingSpinner = submitButton.querySelector('.loading-spinner');
    const departureInput = document.getElementById('departureInput');
    const destinationInput = document.getElementById('destinationInput');
    const departureDate = document.getElementById('departure-date').value;
    const returnDate = document.getElementById('return-date').value;
    const passengers = document.getElementById('passengers').value;

    // Extract IATA codes from inputs
    const fromIata = departureInput.dataset.iata;
    const toIata = destinationInput.dataset.iata;

    // Enhanced validation with better UX
    if (!fromIata || !toIata) {
      showNotification('Please select valid departure and destination airports from the dropdown.', 'error');
      return;
    }

    if (!departureDate) {
      showNotification('Please select a departure date.', 'error');
      return;
    }

    // Show loading state with animation
    submitButton.disabled = true;
    buttonContent.style.opacity = '0';
    loadingSpinner.classList.remove('opacity-0');
    loadingSpinner.classList.add('opacity-100');

    // Show loading in results table
    flightResultsBody.innerHTML = `
      <tr>
        <td colspan="5" class="text-center py-12">
          <div class="flex flex-col items-center">
            <div class="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mb-4"></div>
            <p class="text-gray-600 text-lg">Searching for the best flights...</p>
            <p class="text-gray-400 text-sm mt-2">This may take a few moments</p>
          </div>
        </td>
      </tr>
    `;

    try {
      // Search flights with artificial delay for better UX
      const searchParams = {
        from: fromIata,
        to: toIata,
        departure_date: departureDate,
        return_date: returnDate,
        passengers: passengers
      };

      // Add a small delay to show loading animation
      await new Promise(resolve => setTimeout(resolve, 1500));

      const flights = await searchFlights(searchParams);
      displayFlightResults(flights);

      if (flights.length > 0) {
        showNotification(`Found ${flights.length} flight${flights.length > 1 ? 's' : ''} for your search!`, 'success');
      }

    } catch (error) {
      console.error('Search error:', error);
      showNotification('Sorry, there was an error searching for flights. Please try again.', 'error');
      flightResultsBody.innerHTML = `
        <tr>
          <td colspan="5" class="text-center py-8 text-red-500">
            <div class="flex flex-col items-center">
              <svg class="w-12 h-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <p>Unable to search flights at this time</p>
            </div>
          </td>
        </tr>
      `;
    } finally {
      // Reset button state
      submitButton.disabled = false;
      buttonContent.style.opacity = '1';
      loadingSpinner.classList.add('opacity-0');
      loadingSpinner.classList.remove('opacity-100');
    }
  });

  function displayFlightResults(flights) {
    flightResultsBody.innerHTML = ""; // Clear previous results

    if (flights.length === 0) {
      const noResultsRow = flightResultsBody.insertRow();
      const noResultsCell = noResultsRow.insertCell();
      noResultsCell.colSpan = 5;
      noResultsCell.className = "text-center py-12";
      noResultsCell.innerHTML = `
        <div class="flex flex-col items-center">
          <svg class="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No flights found</h3>
          <p class="text-gray-500">Try adjusting your search criteria or check different dates</p>
        </div>
      `;
      return;
    }

    flights.forEach((flight, index) => {
      const row = flightResultsBody.insertRow();
      row.className = "group cursor-pointer transition-all duration-300 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50";
      row.style.animationDelay = `${index * 100}ms`;
      row.classList.add('animate-fadeInUp');

      // Airline cell with logo placeholder
      const airlineCell = row.insertCell();
      airlineCell.className = "px-8 py-6 whitespace-nowrap";
      airlineCell.innerHTML = `
        <div class="flex items-center">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"/>
            </svg>
          </div>
          <div>
            <div class="text-sm font-bold text-gray-900">${flight.airline}</div>
            <div class="text-xs text-gray-500">${flight.duration || 'Direct flight'}</div>
          </div>
        </div>
      `;

      // Price cell with styling
      const priceCell = row.insertCell();
      priceCell.className = "px-8 py-6 whitespace-nowrap";
      priceCell.innerHTML = `
        <div class="text-right">
          <div class="text-2xl font-bold text-green-600">${flight.price}</div>
          <div class="text-xs text-gray-500">per person</div>
        </div>
      `;

      // Departure cell
      const departureCell = row.insertCell();
      departureCell.className = "px-8 py-6 whitespace-nowrap";
      departureCell.innerHTML = `
        <div class="flex items-center">
          <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd"/>
          </svg>
          <div>
            <div class="text-sm font-medium text-gray-900">${flight.departure}</div>
            <div class="text-xs text-gray-500">Departure</div>
          </div>
        </div>
      `;

      // Arrival cell
      const arrivalCell = row.insertCell();
      arrivalCell.className = "px-8 py-6 whitespace-nowrap";
      arrivalCell.innerHTML = `
        <div class="flex items-center">
          <svg class="w-4 h-4 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-10.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 7.414V11a1 1 0 102 0V7.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd"/>
          </svg>
          <div>
            <div class="text-sm font-medium text-gray-900">${flight.arrival}</div>
            <div class="text-xs text-gray-500">Arrival</div>
          </div>
        </div>
      `;

      // Action cell with book button
      const actionCell = row.insertCell();
      actionCell.className = "px-8 py-6 whitespace-nowrap text-right";
      actionCell.innerHTML = `
        <button class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-2 rounded-full text-sm font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
          Select Flight
        </button>
      `;
    });
  }

  // Notification system
  function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;

    const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
    notification.classList.add(bgColor, 'text-white');

    notification.innerHTML = `
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
          ${type === 'success' ?
            '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>' :
            type === 'error' ?
            '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>' :
            '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>'
          }
        </svg>
        <span>${message}</span>
      </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
      notification.classList.add('translate-x-full');
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 5000);
  }
});
