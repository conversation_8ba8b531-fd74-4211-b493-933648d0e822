{"name": "school_project", "version": "1.0.0", "description": "Flight Search Application with Node.js Backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "build-css": "tailwindcss -i ./src/input.css -o ./dist/output.css --watch", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["flight", "search", "travel", "nodejs", "express"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0"}, "devDependencies": {"tailwindcss": "^4.1.10"}}