[{"/home/<USER>/Desktop/School_Project/skysearch-react/src/index.js": "1", "/home/<USER>/Desktop/School_Project/skysearch-react/src/App.js": "2", "/home/<USER>/Desktop/School_Project/skysearch-react/src/reportWebVitals.js": "3"}, {"size": 535, "mtime": 1750483982835, "results": "4", "hashOfConfig": "5"}, {"size": 13777, "mtime": 1750484259718, "results": "6", "hashOfConfig": "5"}, {"size": 362, "mtime": 1750483982835, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1b716j5", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Desktop/School_Project/skysearch-react/src/index.js", [], [], "/home/<USER>/Desktop/School_Project/skysearch-react/src/App.js", [], [], "/home/<USER>/Desktop/School_Project/skysearch-react/src/reportWebVitals.js", [], []]