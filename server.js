const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// Mock airport data
const airports = [
  { iata: 'SIN', name: 'Singapore Changi Airport', country: 'Singapore', city: 'Singapore' },
  { iata: 'KUL', name: 'Kuala Lumpur International Airport', country: 'Malaysia', city: 'Kuala Lumpur' },
  { iata: 'JFK', name: 'John F. Kennedy International Airport', country: 'United States', city: 'New York' },
  { iata: 'LHR', name: 'London Heathrow Airport', country: 'United Kingdom', city: 'London' },
  { iata: 'CDG', name: 'Charles de Gaulle Airport', country: 'France', city: 'Paris' },
  { iata: 'NRT', name: 'Narita International Airport', country: 'Japan', city: 'Tokyo' },
  { iata: 'LAX', name: 'Los Angeles International Airport', country: 'United States', city: 'Los Angeles' },
  { iata: 'DXB', name: 'Dubai International Airport', country: 'United Arab Emirates', city: 'Dubai' },
  { iata: 'BKK', name: 'Suvarnabhumi Airport', country: 'Thailand', city: 'Bangkok' },
  { iata: 'ICN', name: 'Incheon International Airport', country: 'South Korea', city: 'Seoul' },
  { iata: 'HKG', name: 'Hong Kong International Airport', country: 'Hong Kong', city: 'Hong Kong' },
  { iata: 'SYD', name: 'Sydney Kingsford Smith Airport', country: 'Australia', city: 'Sydney' },
  { iata: 'FRA', name: 'Frankfurt Airport', country: 'Germany', city: 'Frankfurt' },
  { iata: 'AMS', name: 'Amsterdam Airport Schiphol', country: 'Netherlands', city: 'Amsterdam' },
  { iata: 'MAD', name: 'Madrid-Barajas Airport', country: 'Spain', city: 'Madrid' }
];

// Mock flight data
const flights = [
  {
    id: 1,
    airline: 'Singapore Airlines',
    price: '550 SGD',
    departure: 'SIN 10:00',
    arrival: 'KUL 11:15',
    from: 'SIN',
    to: 'KUL',
    duration: '1h 15m'
  },
  {
    id: 2,
    airline: 'British Airways',
    price: '1480 SGD',
    departure: 'SIN 12:30',
    arrival: 'JFK 23:45',
    from: 'SIN',
    to: 'JFK',
    duration: '18h 15m'
  },
  {
    id: 3,
    airline: 'Emirates',
    price: '1550 SGD',
    departure: 'SIN 15:15',
    arrival: 'LHR 22:00',
    from: 'SIN',
    to: 'LHR',
    duration: '13h 45m'
  },
  {
    id: 4,
    airline: 'Air France',
    price: '1200 SGD',
    departure: 'SIN 08:30',
    arrival: 'CDG 19:45',
    from: 'SIN',
    to: 'CDG',
    duration: '12h 15m'
  },
  {
    id: 5,
    airline: 'Japan Airlines',
    price: '800 SGD',
    departure: 'SIN 14:20',
    arrival: 'NRT 22:30',
    from: 'SIN',
    to: 'NRT',
    duration: '7h 10m'
  }
];

// API Routes

// Search airports
app.get('/api/airports/search', (req, res) => {
  const { q } = req.query;
  
  if (!q || q.length < 2) {
    return res.json([]);
  }
  
  const query = q.toUpperCase();
  const filteredAirports = airports.filter(airport => 
    airport.name.toUpperCase().includes(query) ||
    airport.iata.toUpperCase().includes(query) ||
    airport.city.toUpperCase().includes(query) ||
    airport.country.toUpperCase().includes(query)
  );
  
  res.json(filteredAirports.slice(0, 10)); // Limit to 10 results
});

// Get airport by IATA code
app.get('/api/airports/:iata', (req, res) => {
  const { iata } = req.params;
  const airport = airports.find(a => a.iata.toUpperCase() === iata.toUpperCase());
  
  if (!airport) {
    return res.status(404).json({ error: 'Airport not found' });
  }
  
  res.json(airport);
});

// Search flights
app.get('/api/flights/search', (req, res) => {
  const { from, to, departure_date, return_date, passengers } = req.query;
  
  // For demo purposes, return all flights or filter by from/to if provided
  let filteredFlights = flights;
  
  if (from) {
    filteredFlights = filteredFlights.filter(flight => 
      flight.from.toUpperCase() === from.toUpperCase()
    );
  }
  
  if (to) {
    filteredFlights = filteredFlights.filter(flight => 
      flight.to.toUpperCase() === to.toUpperCase()
    );
  }
  
  // Add some randomization to make it more realistic
  const randomFlights = [...filteredFlights];
  if (randomFlights.length === 0) {
    // If no specific flights found, return some random ones
    randomFlights.push(...flights.slice(0, 3));
  }
  
  res.json({
    flights: randomFlights,
    search_params: {
      from,
      to,
      departure_date,
      return_date,
      passengers: passengers || 1
    }
  });
});

// Serve the main HTML file
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
