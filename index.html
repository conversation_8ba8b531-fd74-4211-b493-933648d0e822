<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flight Search - School Project</title>
    <link rel="stylesheet" href="tailwind.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600">
    <main class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <div id="logo-container" class="mb-4">
                <img src="img/airplane_logo.jpg" alt="Airplane Logo" class="w-24 h-24 mx-auto rounded-full shadow-lg">
            </div>
            <h1 class="text-4xl font-bold text-white mb-2">Flight Search</h1>
            <p class="text-blue-100">Find the best flights for your next adventure</p>
        </div>

        <!-- Search Form -->
        <section id="search-form" class="max-w-4xl mx-auto mb-8">
            <div class="bg-white rounded-lg shadow-xl p-6">
                <form id="flight-search-form" class="space-y-6">

                    <!-- Airport Selection Row -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="autocomplete">
                            <label for="departure-airport" class="block text-sm font-medium text-gray-700 mb-2">Departure Airport</label>
                            <input id="departureInput" type="text" placeholder="Search departure airport..."
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200">
                            <div id="departureDropdown" class="autocomplete-items"></div>
                        </div>

                        <div class="autocomplete">
                            <label for="destination-airport" class="block text-sm font-medium text-gray-700 mb-2">Destination Airport</label>
                            <input id="destinationInput" type="text" placeholder="Search destination airport..."
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200">
                            <div id="destinationDropdown" class="autocomplete-items"></div>
                        </div>
                    </div>
                    <!-- Date and Passenger Selection Row -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="departure-date" class="block text-sm font-medium text-gray-700 mb-2">Departure Date</label>
                            <input type="date" id="departure-date" name="departure-date" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200">
                        </div>

                        <div>
                            <label for="return-date" class="block text-sm font-medium text-gray-700 mb-2">Return Date (Optional)</label>
                            <input type="date" id="return-date" name="return-date"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200">
                        </div>

                        <div>
                            <label for="passengers" class="block text-sm font-medium text-gray-700 mb-2">Passengers</label>
                            <input type="number" id="passengers" name="passengers" min="1" value="1" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200">
                        </div>
                    </div>

                    <!-- Search Button -->
                    <div class="text-center">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition duration-200 transform hover:scale-105 shadow-lg">
                            <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Search Flights
                        </button>
                    </div>

                    <!-- Hidden airport results containers -->
                    <section id="airport-results" class="hidden">
                        <div id="departureAirportResults" class="airport-results-box"></div>
                        <div id="destinationAirportResults" class="airport-results-box"></div>
                    </section>

                </form>
            </div>
        </section>
        <!-- Flight Results -->
        <section id="flight-results" class="max-w-4xl mx-auto">
            <div id="result-box" class="bg-white rounded-lg shadow-xl overflow-hidden">
                <div class="bg-gray-50 px-6 py-4 border-b">
                    <h2 class="text-xl font-semibold text-gray-800">Flight Results</h2>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="px-6 py-4 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">Airline</th>
                                <th class="px-6 py-4 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">Price</th>
                                <th class="px-6 py-4 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">Departure</th>
                                <th class="px-6 py-4 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">Arrival</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200"></tbody>
                    </table>
                </div>
            </div>
        </section>
    </main>
    <script src="script.js"></script>
</body>
</html>
