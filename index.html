<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SkySearch - Find Your Perfect Flight</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <!-- Stylesheets -->
    <link rel="stylesheet" href="tailwind.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-800 relative overflow-x-hidden">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-10 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-300 opacity-10 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
        <div class="absolute top-40 left-40 w-80 h-80 bg-pink-300 opacity-10 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
    </div>

    <main class="relative z-10 container mx-auto px-4 py-8">
        <!-- Enhanced Header -->
        <div class="text-center mb-16">
            <div id="logo-container" class="mb-8 relative">
                <div class="w-16 h-16 mx-auto bg-gradient-to-br from-white via-blue-50 to-indigo-100 rounded-2xl shadow-2xl flex items-center justify-center transform hover:scale-110 hover:rotate-3 transition-all duration-500 border border-white/20">
                    <svg class="w-7 h-7 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"/>
                    </svg>
                </div>
                <div class="absolute -top-2 -right-2 w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full animate-pulse shadow-lg"></div>
            </div>

            <div class="space-y-6">
                <h1 class="text-5xl md:text-7xl font-black text-transparent bg-clip-text bg-gradient-to-r from-white via-blue-100 to-indigo-200 mb-6 tracking-tight leading-tight">
                    SkySearch
                </h1>
                <div class="max-w-2xl mx-auto">
                    <p class="text-xl md:text-2xl text-blue-100 font-light tracking-wide leading-relaxed mb-4">
                        Discover your next adventure in the clouds
                    </p>
                    <p class="text-sm md:text-base text-blue-200/80 font-normal">
                        Find the perfect flight with our intelligent search engine
                    </p>
                </div>

                <!-- Enhanced decorative elements -->
                <div class="flex justify-center items-center space-x-3 mt-8">
                    <div class="w-3 h-3 bg-gradient-to-r from-white to-blue-200 rounded-full animate-bounce"></div>
                    <div class="w-2 h-2 bg-gradient-to-r from-blue-300 to-indigo-300 rounded-full animate-bounce animation-delay-200"></div>
                    <div class="w-3 h-3 bg-gradient-to-r from-indigo-300 to-purple-300 rounded-full animate-bounce animation-delay-400"></div>
                    <div class="w-2 h-2 bg-gradient-to-r from-purple-300 to-pink-300 rounded-full animate-bounce animation-delay-600"></div>
                </div>
            </div>
        </div>

        <!-- Enhanced Search Form -->
        <section id="search-form" class="max-w-6xl mx-auto mb-16">
            <div class="relative">
                <!-- Background decoration -->
                <div class="absolute inset-0 bg-gradient-to-r from-white/10 to-white/5 rounded-3xl blur-xl"></div>

                <div class="relative bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/30 p-8 md:p-12 transform hover:scale-[1.01] transition-all duration-500">
                    <!-- Form header -->
                    <div class="text-center mb-12">
                        <div class="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl mb-4 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"/>
                            </svg>
                        </div>
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-3">Plan Your Journey</h2>
                        <p class="text-lg text-gray-600 max-w-md mx-auto leading-relaxed">
                            Enter your travel details to discover amazing flight options
                        </p>
                    </div>

                    <form id="flight-search-form" class="space-y-10">

                    <!-- Enhanced Airport Selection -->
                    <div class="space-y-8">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <div class="autocomplete group">
                                <label for="departure-airport" class="flex items-center text-sm font-bold text-gray-700 mb-4 tracking-wide">
                                    <div class="w-8 h-8 bg-gradient-to-r from-emerald-400 to-green-500 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    DEPARTURE AIRPORT
                                </label>
                                <div class="relative group">
                                    <input id="departureInput" type="text" placeholder="Where are you flying from?"
                                           class="w-full px-6 py-5 pl-14 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-emerald-500/20 focus:border-emerald-500 transition-all duration-300 text-lg placeholder-gray-400 bg-gradient-to-r from-gray-50 to-white group-hover:from-white group-hover:to-gray-50 shadow-sm hover:shadow-md">
                                    <div class="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 bg-emerald-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-3 h-3 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        </svg>
                                    </div>
                                </div>
                                <div id="departureDropdown" class="autocomplete-items"></div>
                            </div>

                            <div class="autocomplete group">
                                <label for="destination-airport" class="flex items-center text-sm font-bold text-gray-700 mb-4 tracking-wide">
                                    <div class="w-8 h-8 bg-gradient-to-r from-rose-400 to-red-500 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-10.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 7.414V11a1 1 0 102 0V7.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    DESTINATION AIRPORT
                                </label>
                                <div class="relative group">
                                    <input id="destinationInput" type="text" placeholder="Where are you going?"
                                           class="w-full px-6 py-5 pl-14 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-rose-500/20 focus:border-rose-500 transition-all duration-300 text-lg placeholder-gray-400 bg-gradient-to-r from-gray-50 to-white group-hover:from-white group-hover:to-gray-50 shadow-sm hover:shadow-md">
                                    <div class="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 bg-rose-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-3 h-3 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        </svg>
                                    </div>
                                </div>
                                <div id="destinationDropdown" class="autocomplete-items"></div>
                            </div>
                        </div>

                        <!-- Flight direction indicator -->
                        <div class="flex justify-center">
                            <div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform duration-300">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <!-- Enhanced Date and Passenger Selection -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <div class="group">
                            <label for="departure-date" class="flex items-center text-sm font-bold text-gray-700 mb-4 tracking-wide">
                                <div class="w-8 h-8 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                                DEPARTURE DATE
                            </label>
                            <div class="relative">
                                <input type="date" id="departure-date" name="departure-date" required
                                       class="w-full px-6 py-5 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 text-lg bg-gradient-to-r from-gray-50 to-white hover:from-white hover:to-gray-50 shadow-sm hover:shadow-md">
                            </div>
                        </div>

                        <div class="group">
                            <label for="return-date" class="flex items-center text-sm font-bold text-gray-700 mb-4 tracking-wide">
                                <div class="w-8 h-8 bg-gradient-to-r from-purple-400 to-violet-500 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                                RETURN DATE
                                <span class="ml-3 px-2 py-1 text-xs text-purple-600 bg-purple-100 rounded-full font-medium">Optional</span>
                            </label>
                            <div class="relative">
                                <input type="date" id="return-date" name="return-date"
                                       class="w-full px-6 py-5 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-purple-500/20 focus:border-purple-500 transition-all duration-300 text-lg bg-gradient-to-r from-gray-50 to-white hover:from-white hover:to-gray-50 shadow-sm hover:shadow-md">
                            </div>
                        </div>

                        <div class="group">
                            <label for="passengers" class="flex items-center text-sm font-bold text-gray-700 mb-4 tracking-wide">
                                <div class="w-8 h-8 bg-gradient-to-r from-amber-400 to-orange-500 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                                    </svg>
                                </div>
                                PASSENGERS
                            </label>
                            <div class="relative">
                                <input type="number" id="passengers" name="passengers" min="1" max="9" value="1" required
                                       class="w-full px-6 py-5 pr-16 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-amber-500/20 focus:border-amber-500 transition-all duration-300 text-lg bg-gradient-to-r from-gray-50 to-white hover:from-white hover:to-gray-50 shadow-sm hover:shadow-md">
                                <div class="absolute right-4 top-1/2 transform -translate-y-1/2 flex flex-col space-y-1">
                                    <button type="button" class="passenger-increment w-6 h-6 bg-amber-100 hover:bg-amber-200 rounded-lg flex items-center justify-center text-amber-600 hover:text-amber-700 transition-all duration-200">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                                        </svg>
                                    </button>
                                    <button type="button" class="passenger-decrement w-6 h-6 bg-amber-100 hover:bg-amber-200 rounded-lg flex items-center justify-center text-amber-600 hover:text-amber-700 transition-all duration-200">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Search Button -->
                    <div class="text-center pt-8">
                        <div class="relative inline-block">
                            <!-- Button glow effect -->
                            <div class="absolute inset-0 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-3xl blur-lg opacity-30 group-hover:opacity-50 transition-opacity duration-300"></div>

                            <button type="submit" class="group relative inline-flex items-center justify-center px-16 py-6 text-xl font-bold text-white bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-3xl shadow-2xl hover:shadow-3xl transform hover:scale-105 hover:-translate-y-1 transition-all duration-500 overflow-hidden border border-white/20">
                                <!-- Animated background shimmer -->
                                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                                <!-- Button content -->
                                <div class="relative flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-white/20 rounded-xl flex items-center justify-center">
                                        <svg class="w-5 h-5 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                        </svg>
                                    </div>
                                    <span class="tracking-wide">Search Flights</span>
                                    <div class="w-8 h-8 bg-white/20 rounded-xl flex items-center justify-center transform group-hover:translate-x-1 transition-transform duration-300">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                                        </svg>
                                    </div>
                                </div>

                                <!-- Loading spinner (hidden by default) -->
                                <div class="absolute inset-0 flex items-center justify-center opacity-0 loading-spinner">
                                    <div class="w-8 h-8 border-4 border-white/30 border-t-white rounded-full animate-spin"></div>
                                </div>
                            </button>
                        </div>

                        <!-- Enhanced tips -->
                        <div class="mt-8 space-y-3">
                            <div class="flex items-center justify-center space-x-2 text-sm text-gray-600">
                                <div class="w-2 h-2 bg-indigo-400 rounded-full"></div>
                                <span>Use airport codes (LAX, JFK) for faster search</span>
                            </div>
                            <div class="flex items-center justify-center space-x-2 text-sm text-gray-600">
                                <div class="w-2 h-2 bg-purple-400 rounded-full"></div>
                                <span>Compare prices from multiple airlines</span>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden airport results containers -->
                    <section id="airport-results" class="hidden">
                        <div id="departureAirportResults" class="airport-results-box"></div>
                        <div id="destinationAirportResults" class="airport-results-box"></div>
                    </section>

                </form>
            </div>
        </section>
        <!-- Flight Results -->
        <section id="flight-results" class="max-w-6xl mx-auto">
            <div id="result-box" class="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 overflow-hidden transform hover:scale-[1.01] transition-all duration-300">
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 px-8 py-6 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-2xl font-bold text-gray-800 flex items-center">
                                <svg class="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                                </svg>
                                Available Flights
                            </h2>
                            <p class="text-gray-600 mt-1">Choose the perfect flight for your journey</p>
                        </div>
                        <div class="hidden md:flex items-center space-x-4 text-sm text-gray-500">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
                                Best Price
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-blue-400 rounded-full mr-2"></div>
                                Fastest
                            </div>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
                            <tr>
                                <th class="px-8 py-5 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        Airline
                                    </div>
                                </th>
                                <th class="px-8 py-5 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
                                        </svg>
                                        Price
                                    </div>
                                </th>
                                <th class="px-8 py-5 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                        </svg>
                                        Departure
                                    </div>
                                </th>
                                <th class="px-8 py-5 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                        </svg>
                                        Arrival
                                    </div>
                                </th>
                                <th class="px-8 py-5 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">Action</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-100"></tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="max-w-6xl mx-auto mt-16 mb-12">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-white mb-4">Why Choose SkySearch?</h2>
                <p class="text-blue-100 text-lg">Experience the future of flight booking</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-center hover:bg-white/20 transition-all duration-300 transform hover:scale-105">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 features-icon text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-2">Lightning Fast</h3>
                    <p class="text-blue-100">Search thousands of flights in seconds with our advanced algorithms</p>
                </div>

                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-center hover:bg-white/20 transition-all duration-300 transform hover:scale-105">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 features-icon text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-2">Best Prices</h3>
                    <p class="text-blue-100">Compare prices from multiple airlines to find the best deals</p>
                </div>

                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-center hover:bg-white/20 transition-all duration-300 transform hover:scale-105">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 features-icon text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-2">Secure & Safe</h3>
                    <p class="text-blue-100">Your data is protected with enterprise-grade security</p>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="relative z-10 bg-black/20 backdrop-blur-sm border-t border-white/10 mt-16">
        <div class="container mx-auto px-4 py-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-gradient-to-br from-white to-blue-100 rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-white">SkySearch</h3>
                    </div>
                    <p class="text-blue-100 mb-4 max-w-md">
                        Your trusted companion for finding the perfect flights. We make travel planning simple, fast, and enjoyable.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        <a href="#" class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-blue-100 hover:text-white transition-colors">About Us</a></li>
                        <li><a href="#" class="text-blue-100 hover:text-white transition-colors">Contact</a></li>
                        <li><a href="#" class="text-blue-100 hover:text-white transition-colors">Help Center</a></li>
                        <li><a href="#" class="text-blue-100 hover:text-white transition-colors">Privacy Policy</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4">Services</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-blue-100 hover:text-white transition-colors">Flight Search</a></li>
                        <li><a href="#" class="text-blue-100 hover:text-white transition-colors">Hotel Booking</a></li>
                        <li><a href="#" class="text-blue-100 hover:text-white transition-colors">Car Rental</a></li>
                        <li><a href="#" class="text-blue-100 hover:text-white transition-colors">Travel Insurance</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-white/10 mt-8 pt-8 text-center">
                <p class="text-blue-100">
                    © 2024 SkySearch. Made with ❤️ for travelers worldwide.
                </p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
