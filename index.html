<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>School Project</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <main>
        <div id="logo-container">
            <img src="img/airplane_logo.jpg" alt="Airplane Logo"> 
        </div>
        <section id="search-form">
            <form id="flight-search-form">

                <div class="autocomplete">
                    <label for="departure-airport">Departure Airport:</label>
                    <input id="departureInput" type="text" placeholder="Departure Airport">
                    <div id="departureDropdown" class="autocomplete-items"></div>
                </div>
            
                <div class="autocomplete">
                    <label for="destination-airport">Destination Airport:</label>
                    <input id="destinationInput" type="text" placeholder="Destination Airport">
                    <div id="destinationDropdown" class="autocomplete-items"></div>
                </div>
                <label for="departure-date">Departure Date:</label>
                <input type="date" id="departure-date" name="departure-date" required>
                <label for="return-date">Return Date:</label>
                <input type="date" id="return-date" name="return-date">
                <label for="passengers">Passengers:</label>
                <input type="number" id="passengers" name="passengers" min="1" value="1" required>
                
                <button type="submit">Search Flights</button>



                <section id="airport-results">
                    <div id="departureAirportResults" class="airport-results-box"></div> 
                    <div id="destinationAirportResults" class="airport-results-box"></div> 
                </section>


            </form>
        </section>
        <section id="flight-results">
            <div id="result-box">
                <table>
                  <thead>
                    <tr>
                      <th>Airline</th>
                      <th>Price</th>
                      <th>Departure</th>
                      <th>Arrival</th>
                    </tr>
                  </thead>
                  <tbody></tbody>
                </table>
              </div>
        </section>
    </main>
    <script src="script.js"></script>
</body>
</html>
