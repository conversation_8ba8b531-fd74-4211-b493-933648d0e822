<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SkySearch - Find Your Perfect Flight</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <!-- Stylesheets -->
    <link rel="stylesheet" href="tailwind.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="min-h-screen bg-gray-50 font-sans antialiased">
    <!-- Clean, accessible design with proper contrast -->

    <!-- Header with proper contrast and accessibility -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"/>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">SkySearch</h1>
                        <p class="text-sm text-gray-600">Find your perfect flight</p>
                    </div>
                </div>
                <nav class="hidden md:flex space-x-6">
                    <a href="#" class="text-gray-600 hover:text-gray-900 font-medium">Flights</a>
                    <a href="#" class="text-gray-600 hover:text-gray-900 font-medium">Hotels</a>
                    <a href="#" class="text-gray-600 hover:text-gray-900 font-medium">Support</a>
                </nav>
            </div>
        </div>
    </header>

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- Accessible Search Form -->
        <section id="search-form" class="mb-12" aria-labelledby="search-heading">
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 md:p-8">
                <div class="mb-8">
                    <h2 id="search-heading" class="text-2xl font-bold text-gray-900 mb-2">Search Flights</h2>
                    <p class="text-gray-600">Find and compare flights from multiple airlines</p>
                </div>

                <form id="flight-search-form" class="space-y-6" role="search" aria-label="Flight search form">

                    <!-- Airport Selection with Proper Accessibility -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="autocomplete">
                            <label for="departureInput" class="block text-sm font-semibold text-gray-700 mb-2">
                                From <span class="text-red-500" aria-label="required">*</span>
                            </label>
                            <div class="relative">
                                <input
                                    id="departureInput"
                                    type="text"
                                    placeholder="Enter departure city or airport"
                                    required
                                    aria-describedby="departure-help"
                                    class="w-full px-4 py-3 pl-12 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-base bg-white hover:border-gray-400"
                                    autocomplete="off"
                                >
                                <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                                    </svg>
                                </div>
                            </div>
                            <div id="departure-help" class="text-xs text-gray-500 mt-1">
                                Try "New York" or "JFK"
                            </div>
                            <div id="departureDropdown" class="autocomplete-items" role="listbox" aria-label="Departure airport suggestions"></div>
                        </div>

                        <div class="autocomplete">
                            <label for="destinationInput" class="block text-sm font-semibold text-gray-700 mb-2">
                                To <span class="text-red-500" aria-label="required">*</span>
                            </label>
                            <div class="relative">
                                <input
                                    id="destinationInput"
                                    type="text"
                                    placeholder="Enter destination city or airport"
                                    required
                                    aria-describedby="destination-help"
                                    class="w-full px-4 py-3 pl-12 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-base bg-white hover:border-gray-400"
                                    autocomplete="off"
                                >
                                <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                </div>
                            </div>
                            <div id="destination-help" class="text-xs text-gray-500 mt-1">
                                Try "London" or "LHR"
                            </div>
                            <div id="destinationDropdown" class="autocomplete-items" role="listbox" aria-label="Destination airport suggestions"></div>
                        </div>
                    </div>
                    <!-- Date and Passenger Selection with Clear Labels -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="departure-date" class="block text-sm font-semibold text-gray-700 mb-2">
                                Departure Date <span class="text-red-500" aria-label="required">*</span>
                            </label>
                            <input
                                type="date"
                                id="departure-date"
                                name="departure-date"
                                required
                                class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-base bg-white hover:border-gray-400"
                            >
                        </div>

                        <div>
                            <label for="return-date" class="block text-sm font-semibold text-gray-700 mb-2">
                                Return Date
                                <span class="text-xs text-gray-500 font-normal">(Optional)</span>
                            </label>
                            <input
                                type="date"
                                id="return-date"
                                name="return-date"
                                class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-base bg-white hover:border-gray-400"
                            >
                        </div>

                        <div>
                            <label for="passengers" class="block text-sm font-semibold text-gray-700 mb-2">
                                Passengers <span class="text-red-500" aria-label="required">*</span>
                            </label>
                            <div class="relative">
                                <input
                                    type="number"
                                    id="passengers"
                                    name="passengers"
                                    min="1"
                                    max="9"
                                    value="1"
                                    required
                                    aria-describedby="passengers-help"
                                    class="w-full px-4 py-3 pr-20 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-base bg-white hover:border-gray-400"
                                >
                                <div class="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                                    <button
                                        type="button"
                                        class="passenger-decrement w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded border border-gray-300 flex items-center justify-center text-gray-600 hover:text-gray-800 transition-colors duration-200"
                                        aria-label="Decrease passengers"
                                    >
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                                        </svg>
                                    </button>
                                    <button
                                        type="button"
                                        class="passenger-increment w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded border border-gray-300 flex items-center justify-center text-gray-600 hover:text-gray-800 transition-colors duration-200"
                                        aria-label="Increase passengers"
                                    >
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div id="passengers-help" class="text-xs text-gray-500 mt-1">
                                Maximum 9 passengers
                            </div>
                        </div>
                    </div>

                    <!-- Prominent, Accessible Search Button -->
                    <div class="flex flex-col sm:flex-row gap-4 pt-6">
                        <button
                            type="submit"
                            class="flex-1 bg-blue-600 hover:bg-blue-700 focus:bg-blue-700 text-white font-semibold py-4 px-8 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                            id="search-button"
                            aria-describedby="search-status"
                        >
                            <span class="flex items-center justify-center space-x-2">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                                <span class="search-text">Search Flights</span>
                                <span class="loading-text hidden">Searching...</span>
                            </span>
                        </button>

                        <button
                            type="button"
                            class="sm:w-auto bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-4 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                            onclick="clearForm()"
                        >
                            Clear
                        </button>
                    </div>

                    <!-- Status and Help Text -->
                    <div class="mt-4 space-y-2">
                        <div id="search-status" class="text-sm text-gray-600" aria-live="polite" aria-atomic="true"></div>
                        <div class="text-xs text-gray-500">
                            💡 Tip: Use airport codes like "LAX" or "JFK" for faster results
                        </div>
                    </div>

                    <!-- Hidden airport results containers -->
                    <section id="airport-results" class="hidden">
                        <div id="departureAirportResults" class="airport-results-box"></div>
                        <div id="destinationAirportResults" class="airport-results-box"></div>
                    </section>

                </form>
            </div>
        </section>
        <!-- Results Section (Hidden until search is performed) -->
        <section id="results-section" class="hidden mb-12" aria-labelledby="results-heading">
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 md:p-8">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                    <div>
                        <h2 id="results-heading" class="text-2xl font-bold text-gray-900 mb-1">Flight Results</h2>
                        <p id="results-summary" class="text-gray-600"></p>
                    </div>

                    <!-- Sort Options -->
                    <div class="mt-4 sm:mt-0">
                        <label for="sort-select" class="sr-only">Sort flights by</label>
                        <select
                            id="sort-select"
                            class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-sm"
                            onchange="sortFlights(this.value)"
                        >
                            <option value="price">Best Price</option>
                            <option value="duration">Shortest Duration</option>
                            <option value="departure">Departure Time</option>
                        </select>
                    </div>
                </div>

                <!-- Flight Results -->
                <div id="flight-results" class="space-y-4" role="region" aria-label="Flight search results">
                    <!-- Results will be populated here -->
                </div>

                <!-- Loading State -->
                <div id="loading-state" class="hidden text-center py-12" role="status" aria-live="polite">
                    <div class="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-4">
                        <svg class="animate-spin w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Searching for flights...</h3>
                    <p class="text-gray-600">Finding the best options for your trip</p>
                </div>

                <!-- No Results State -->
                <div id="no-results-state" class="hidden text-center py-12">
                    <div class="inline-flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full mb-4">
                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">No flights found</h3>
                    <p class="text-gray-600">Try adjusting your search criteria or dates</p>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="mt-16 mb-12" aria-labelledby="features-heading">
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 md:p-8">
                <div class="text-center mb-8">
                    <h2 id="features-heading" class="text-2xl font-bold text-gray-900 mb-2">Why Choose SkySearch?</h2>
                    <p class="text-gray-600">Experience the best in flight booking</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Lightning Fast</h3>
                        <p class="text-gray-600 text-sm">Search thousands of flights in seconds with our advanced algorithms</p>
                    </div>

                    <div class="text-center p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Best Prices</h3>
                        <p class="text-gray-600 text-sm">Compare prices from multiple airlines to find the best deals</p>
                    </div>

                    <div class="text-center p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Secure & Safe</h3>
                        <p class="text-gray-600 text-sm">Your data is protected with enterprise-grade security</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold">SkySearch</h3>
                    </div>
                    <p class="text-gray-300 mb-6 max-w-md">
                        Your trusted companion for finding the perfect flights. We make travel planning simple, fast, and enjoyable.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500" aria-label="Follow us on Twitter">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500" aria-label="Follow us on Facebook">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500" aria-label="Connect with us on LinkedIn">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">About Us</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Contact</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Help Center</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Privacy Policy</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="font-semibold mb-4">Services</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Flight Search</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Hotel Booking</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Car Rental</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Travel Insurance</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-300">
                    © 2024 SkySearch. Made with ❤️ for travelers worldwide.
                </p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script>
        // Clear form function
        function clearForm() {
            document.getElementById('flight-search-form').reset();
            document.getElementById('results-section').classList.add('hidden');
            document.getElementById('search-status').textContent = '';
        }

        // Sort flights function
        function sortFlights(criteria) {
            // This will be implemented when we have actual flight data
            console.log('Sorting by:', criteria);
        }

        // Enhanced form validation
        function validateForm() {
            const departure = document.getElementById('departureInput').value.trim();
            const destination = document.getElementById('destinationInput').value.trim();
            const departureDate = document.getElementById('departure-date').value;

            let isValid = true;
            let errors = [];

            if (!departure) {
                errors.push('Departure airport is required');
                document.getElementById('departureInput').classList.add('error');
            } else {
                document.getElementById('departureInput').classList.remove('error');
            }

            if (!destination) {
                errors.push('Destination airport is required');
                document.getElementById('destinationInput').classList.add('error');
            } else {
                document.getElementById('destinationInput').classList.remove('error');
            }

            if (!departureDate) {
                errors.push('Departure date is required');
                document.getElementById('departure-date').classList.add('error');
            } else {
                document.getElementById('departure-date').classList.remove('error');
            }

            if (errors.length > 0) {
                document.getElementById('search-status').textContent = errors.join(', ');
                document.getElementById('search-status').setAttribute('aria-live', 'assertive');
                isValid = false;
            } else {
                document.getElementById('search-status').textContent = '';
            }

            return isValid;
        }

        // Enhanced passenger counter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const passengerInput = document.getElementById('passengers');
            const incrementBtn = document.querySelector('.passenger-increment');
            const decrementBtn = document.querySelector('.passenger-decrement');

            incrementBtn.addEventListener('click', function() {
                const current = parseInt(passengerInput.value);
                if (current < 9) {
                    passengerInput.value = current + 1;
                }
            });

            decrementBtn.addEventListener('click', function() {
                const current = parseInt(passengerInput.value);
                if (current > 1) {
                    passengerInput.value = current - 1;
                }
            });
        });
    </script>
</body>
</html>
