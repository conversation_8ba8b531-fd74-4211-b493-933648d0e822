@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
.autocomplete {
  position: relative;
}

.autocomplete-items {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.autocomplete-item {
  padding: 0.75rem;
  cursor: pointer;
  border-bottom: 1px solid #e5e7eb;
}

.autocomplete-item:hover {
  background-color: #f3f4f6;
}

.autocomplete-item:last-child {
  border-bottom: none;
}
