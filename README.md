# Flight Search Application

A modern flight search application built with Node.js backend, Express API, and Tailwind CSS frontend.

## Features

- 🔍 **Airport Search**: Real-time airport search with autocomplete
- ✈️ **Flight Search**: Search flights between airports with mock data
- 🎨 **Modern UI**: Beautiful, responsive design with Tailwind CSS
- 🚀 **Fast API**: Local Node.js backend with Express
- 📱 **Responsive**: Works on desktop and mobile devices

## Technology Stack

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **CORS** - Cross-origin resource sharing
- **Mock APIs** - Simulated flight and airport data

### Frontend
- **HTML5** - Semantic markup
- **Tailwind CSS** - Utility-first CSS framework
- **Vanilla JavaScript** - Modern ES6+ features
- **Responsive Design** - Mobile-first approach

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm (Node Package Manager)

### Installation

1. **Clone or navigate to the project directory**
   ```bash
   cd School_Project
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the server**
   ```bash
   npm start
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000`

## API Endpoints

### Airport Search
```
GET /api/airports/search?q={query}
```
Search for airports by name, city, or IATA code.

**Example:**
```bash
curl "http://localhost:3000/api/airports/search?q=london"
```

### Get Airport by IATA
```
GET /api/airports/{iata}
```
Get specific airport information by IATA code.

**Example:**
```bash
curl "http://localhost:3000/api/airports/LHR"
```

### Flight Search
```
GET /api/flights/search?from={iata}&to={iata}&departure_date={date}&passengers={number}
```
Search for flights between airports.

**Example:**
```bash
curl "http://localhost:3000/api/flights/search?from=SIN&to=KUL&passengers=2"
```

## Project Structure

```
School_Project/
├── server.js              # Express server and API routes
├── index.html             # Main HTML file with Tailwind styling
├── script.js              # Frontend JavaScript
├── styles.css             # Custom CSS styles
├── tailwind.css           # Tailwind CSS framework
├── package.json           # Node.js dependencies and scripts
├── test-api.js            # API testing script
└── README.md              # Project documentation
```

## Testing

Run the API tests to verify everything is working:

```bash
node test-api.js
```

## Development

### Available Scripts

- `npm start` - Start the production server
- `npm run dev` - Start the development server
- `node test-api.js` - Run API tests

### Adding New Features

1. **Backend**: Add new routes in `server.js`
2. **Frontend**: Update `script.js` for new functionality
3. **Styling**: Modify `styles.css` or add Tailwind classes

## Mock Data

The application includes mock data for:
- **15 major airports** worldwide
- **5 sample flights** with different airlines and routes

## Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Contributing

1. Fork the project
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is for educational purposes.
