// Simple test script to verify API functionality
const API_BASE_URL = 'http://localhost:3000/api';

async function testAPI() {
  console.log('🧪 Testing Flight Search API...\n');

  try {
    // Test 1: Airport search
    console.log('1. Testing airport search...');
    const airportResponse = await fetch(`${API_BASE_URL}/airports/search?q=singapore`);
    const airports = await airportResponse.json();
    
    if (airports.length > 0) {
      console.log('✅ Airport search working');
      console.log(`   Found: ${airports[0].name} (${airports[0].iata})`);
    } else {
      console.log('❌ Airport search failed - no results');
    }

    // Test 2: Specific airport lookup
    console.log('\n2. Testing specific airport lookup...');
    const specificAirportResponse = await fetch(`${API_BASE_URL}/airports/SIN`);
    const specificAirport = await specificAirportResponse.json();
    
    if (specificAirport.name) {
      console.log('✅ Specific airport lookup working');
      console.log(`   Found: ${specificAirport.name}`);
    } else {
      console.log('❌ Specific airport lookup failed');
    }

    // Test 3: Flight search
    console.log('\n3. Testing flight search...');
    const flightResponse = await fetch(`${API_BASE_URL}/flights/search?from=SIN&to=KUL&passengers=1`);
    const flightData = await flightResponse.json();
    
    if (flightData.flights && flightData.flights.length > 0) {
      console.log('✅ Flight search working');
      console.log(`   Found ${flightData.flights.length} flights`);
      console.log(`   Example: ${flightData.flights[0].airline} - ${flightData.flights[0].price}`);
    } else {
      console.log('❌ Flight search failed - no results');
    }

    console.log('\n🎉 All API tests completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run tests if this file is executed directly
if (typeof window === 'undefined') {
  // Node.js environment
  (async () => {
    const { default: fetch } = await import('node-fetch');
    global.fetch = fetch;
    await testAPI();
  })();
} else {
  // Browser environment
  window.testAPI = testAPI;
}
